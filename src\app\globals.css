@import "tailwindcss";

:root {
  --background: #fffefb;
  --foreground: #2c3e50;
  --primary-green: #4b935e;
  --accent-gold: #d4af37;
  --text-dark: #2c3e50;
  --text-light: #7f8c8d;
  --border-light: #e8e8e8;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary-green);
  --color-accent: var(--accent-gold);
  --font-sans: var(--font-poppins);
  --font-arabic: var(--font-baloo-bhaijaan);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #1a1a1a;
    --foreground: #fffefb;
    --text-dark: #fffefb;
    --text-light: #bdc3c7;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), Arial, Helvetica, sans-serif;
}

/* Custom Animations */
@keyframes spin-slow {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin-slow {
  animation: spin-slow 20s linear infinite;
}

/* Font Classes */
.font-arabic {
  font-family: var(--font-baloo-bhaijaan), Arial, sans-serif;
  direction: rtl;
  text-align: right;
}

/* Smooth Transitions */
* {
  transition: color 0.3s ease, background-color 0.3s ease;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--background);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-green);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-gold);
}
