export default function IslamicPattern({ className = "", opacity = 0.1 }) {
  return (
    <div className={`absolute inset-0 ${className}`} style={{ opacity }}>
      <svg
        className="w-full h-full"
        viewBox="0 0 400 400"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs>
          <pattern
            id="islamic-pattern"
            x="0"
            y="0"
            width="100"
            height="100"
            patternUnits="userSpaceOnUse"
          >
            {/* 8-pointed star pattern */}
            <g transform="translate(50,50)">
              <path
                d="M0,-30 L10,-10 L30,-10 L15,0 L30,10 L10,10 L0,30 L-10,10 L-30,10 L-15,0 L-30,-10 L-10,-10 Z"
                fill="currentColor"
                fillOpacity="0.3"
              />
            </g>

            <path
              d="M0,50 L50,0 M50,0 L100,50 M100,50 L50,100 M50,100 L0,50"
              stroke="currentColor"
              strokeWidth="1"
              strokeOpacity="0.2"
            />

            <g transform="translate(25,25)">
              <path
                d="M-15,-15 Q0,-15 0,0 Q0,15 15,15"
                stroke="currentColor"
                strokeWidth="2"
                fill="none"
                strokeOpacity="0.3"
              />
            </g>
            <g transform="translate(75,25)">
              <path
                d="M15,-15 Q0,-15 0,0 Q0,15 -15,15"
                stroke="currentColor"
                strokeWidth="2"
                fill="none"
                strokeOpacity="0.3"
              />
            </g>
            <g transform="translate(25,75)">
              <path
                d="M-15,15 Q0,15 0,0 Q0,-15 15,-15"
                stroke="currentColor"
                strokeWidth="2"
                fill="none"
                strokeOpacity="0.3"
              />
            </g>
            <g transform="translate(75,75)">
              <path
                d="M15,15 Q0,15 0,0 Q0,-15 -15,-15"
                stroke="currentColor"
                strokeWidth="2"
                fill="none"
                strokeOpacity="0.3"
              />
            </g>
          </pattern>
        </defs>

        <rect
          width="100%"
          height="100%"
          fill="url(#islamic-pattern)"
          className="text-[#4B935E]"
        />
      </svg>
    </div>
  );
}
