import Image from "next/image";

export default function Footer() {
  return (
    <footer className="relative bg-gradient-to-b from-[#2C3E50] to-[#1A252F] text-white">
      {/* Islamic geometric border decorations */}
      <div className="absolute top-0 left-0 right-0 h-2 bg-gradient-to-r from-transparent via-[#4B935E] to-transparent"></div>
      
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20">
        
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12 mb-12">
          
          {/* Organization Info */}
          <div className="lg:col-span-2">
            <div className="flex items-center gap-4 mb-6">
              <div className="relative">
                <div className="absolute inset-0 bg-[#4B935E] rounded-full blur-lg opacity-30"></div>
                <div className="relative bg-white rounded-full p-3 shadow-xl">
                  <Image
                    src="/LOGO/logo.svg"
                    alt="AL-Insan Logo"
                    width={40}
                    height={60}
                    className="filter brightness-0 saturate-100"
                    style={{
                      filter: "invert(29%) sepia(15%) saturate(1729%) hue-rotate(152deg) brightness(91%) contrast(89%)",
                    }}
                  />
                </div>
              </div>
              <div>
                <h3 className="text-2xl font-bold text-white">AL-Insan</h3>
                <p className="text-[#4B935E] font-semibold">Algerian Human Organization</p>
              </div>
            </div>
            
            {/* Islamic Phrase */}
            <div className="mb-6 p-4 bg-[#4B935E]/10 rounded-lg border border-[#4B935E]/20">
              <p className="text-2xl font-arabic text-[#4B935E] mb-2 text-right" dir="rtl">
                بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيم
              </p>
              <p className="text-sm text-white/80 italic">
                "In the name of Allah, the Most Gracious, the Most Merciful"
              </p>
            </div>
            
            <p className="text-white/70 leading-relaxed">
              Empowering Algerian communities through Islamic values, humanitarian aid, and sustainable development programs that restore dignity and hope.
            </p>
          </div>
          
          {/* Contact Information */}
          <div>
            <h4 className="text-lg font-bold text-white mb-6 flex items-center gap-2">
              <svg className="w-5 h-5 text-[#4B935E]" fill="currentColor" viewBox="0 0 20 20">
                <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
              </svg>
              Contact Us
            </h4>
            <div className="space-y-4">
              <div>
                <p className="text-white/60 text-sm mb-1">Phone</p>
                <a 
                  href="tel:+213657715243" 
                  className="text-[#4B935E] hover:text-[#3A7A4A] transition-colors duration-300 font-semibold flex items-center gap-2"
                  aria-label="Call AL-Insan organization"
                >
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 3a1 1 0 011-1h2.153a1 1 0 01.986.836l.74 4.435a1 1 0 01-.54 1.06l-1.548.773a11.037 11.037 0 006.105 6.105l.774-1.548a1 1 0 011.059-.54l4.435.74a1 1 0 01.836.986V17a1 1 0 01-1 1h-2C7.82 18 2 12.18 2 5V3z"/>
                  </svg>
                  +213 657 71 52 43
                </a>
              </div>
              
              <div>
                <p className="text-white/60 text-sm mb-1">Location</p>
                <a 
                  href="https://maps.app.goo.gl/zZTLxgPhvNkJGWqj9" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-[#4B935E] hover:text-[#3A7A4A] transition-colors duration-300 font-semibold flex items-center gap-2"
                  aria-label="View AL-Insan location on Google Maps"
                >
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd"/>
                  </svg>
                  View on Map
                </a>
              </div>
            </div>
          </div>
          
          {/* Social Media Links */}
          <div>
            <h4 className="text-lg font-bold text-white mb-6 flex items-center gap-2">
              <svg className="w-5 h-5 text-[#4B935E]" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-3.22l-1.14 1.14a.5.5 0 01-.708 0L8.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V6h8v6H9.771z" clipRule="evenodd"/>
              </svg>
              Follow Us
            </h4>
            <div className="space-y-4">
              <a 
                href="https://www.youtube.com/@algerianhuman-e2p" 
                target="_blank" 
                rel="noopener noreferrer"
                className="flex items-center gap-3 text-white/70 hover:text-[#4B935E] transition-colors duration-300 group"
                aria-label="Visit AL-Insan YouTube channel"
              >
                <div className="w-8 h-8 bg-red-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                  </svg>
                </div>
                <span className="font-medium">YouTube Channel</span>
              </a>
              
              <a 
                href="https://www.facebook.com/alegrianhuman" 
                target="_blank" 
                rel="noopener noreferrer"
                className="flex items-center gap-3 text-white/70 hover:text-[#4B935E] transition-colors duration-300 group"
                aria-label="Visit AL-Insan Facebook page"
              >
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300">
                  <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                  </svg>
                </div>
                <span className="font-medium">Facebook Page</span>
              </a>
            </div>
          </div>
        </div>
        
        {/* Islamic Geometric Divider */}
        <div className="flex items-center justify-center mb-8">
          <div className="flex-1 h-px bg-gradient-to-r from-transparent to-[#4B935E]/30"></div>
          <div className="px-6">
            <svg className="w-8 h-8 text-[#4B935E]" viewBox="0 0 32 32" fill="currentColor">
              <path d="M16 0 L20 12 L32 16 L20 20 L16 32 L12 20 L0 16 L12 12 Z"/>
            </svg>
          </div>
          <div className="flex-1 h-px bg-gradient-to-l from-transparent to-[#4B935E]/30"></div>
        </div>
        
        {/* Copyright */}
        <div className="text-center">
          <p className="text-white/60 text-sm">
            © 2025 AL-Insan - Algerian Human Organization. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
}
