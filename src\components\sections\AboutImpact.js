import Image from "next/image";
import Icon from "../ui/Icon";

export default function AboutImpact() {
  const impactStats = [
    {
      id: 1,
      number: "15,000+",
      label: "Families Helped",
      iconName: "sacrifice",
    },
    {
      id: 2,
      number: "50+",
      label: "Communities Served",
      iconName: "mosque",
    },
    {
      id: 3,
      number: "200+",
      label: "Clean Water Projects",
      iconName: "water",
    },
    {
      id: 4,
      number: "1,000+",
      label: "Food Baskets Distributed",
      iconName: "food-basket",
    },
  ];

  return (
    <section className="py-16 sm:py-20 lg:py-24 bg-gradient-to-b from-[#FFFEFB] via-[#F8F9FA] to-[#E8F5E8]">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Mission Statement with Logo */}
        <div className="text-center mb-16 sm:mb-20">
          {/* Logo Integration */}
          <div className="flex justify-center mb-8">
            <div className="relative">
              <div className="absolute inset-0 bg-[#4B935E] rounded-full blur-xl opacity-20 scale-110"></div>
              <div className="relative bg-white rounded-full p-6 shadow-2xl border-4 border-[#4B935E]/20">
                <Image
                  src="/LOGO/logo.svg"
                  alt="AL-Insan Logo"
                  width={80}
                  height={120}
                  className="filter brightness-0 saturate-100"
                  style={{
                    filter:
                      "invert(29%) sepia(15%) saturate(1729%) hue-rotate(152deg) brightness(91%) contrast(89%)",
                  }}
                />
              </div>
            </div>
          </div>

          {/* Brief Mission Statement - Maximum 50 words */}
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-[#2C3E50] mb-6">
              Our Mission & Impact
            </h2>

            <p className="text-xl sm:text-2xl text-[#7F8C8D] leading-relaxed font-light max-w-2xl mx-auto">
              Empowering Algerian communities through Islamic values,
              humanitarian aid, and sustainable development programs that
              restore dignity and hope.
            </p>
          </div>
        </div>

        {/* Visually Prominent Impact Statistics */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-6">
          {impactStats.map((stat) => (
            <div
              key={stat.id}
              className="group relative bg-white rounded-xl shadow-lg border-2 border-[#4B935E]/20 p-8 text-center transition-all duration-300 hover:border-[#4B935E]/40 hover:shadow-2xl hover:scale-105"
            >
              {/* Islamic geometric corner decorations - same as DonationOptions but slightly different */}
              <div className="absolute top-2 left-2 w-6 h-6">
                <svg
                  viewBox="0 0 24 24"
                  className="w-full h-full text-[#4B935E] opacity-40"
                >
                  <path d="M0,12 Q0,0 12,0 Q12,12 0,12" fill="currentColor" />
                </svg>
              </div>
              <div className="absolute top-2 right-2 w-6 h-6 rotate-90">
                <svg
                  viewBox="0 0 24 24"
                  className="w-full h-full text-[#4B935E] opacity-40"
                >
                  <path d="M0,12 Q0,0 12,0 Q12,12 0,12" fill="currentColor" />
                </svg>
              </div>
              <div className="absolute bottom-2 left-2 w-6 h-6 -rotate-90">
                <svg
                  viewBox="0 0 24 24"
                  className="w-full h-full text-[#4B935E] opacity-40"
                >
                  <path d="M0,12 Q0,0 12,0 Q12,12 0,12" fill="currentColor" />
                </svg>
              </div>
              <div className="absolute bottom-2 right-2 w-6 h-6 rotate-180">
                <svg
                  viewBox="0 0 24 24"
                  className="w-full h-full text-[#4B935E] opacity-40"
                >
                  <path d="M0,12 Q0,0 12,0 Q12,12 0,12" fill="currentColor" />
                </svg>
              </div>
              {/* Simple Icon */}
              <div className="w-20 h-20 mx-auto mb-6 bg-[#4B935E]/10 rounded-full flex items-center justify-center group-hover:bg-[#4B935E]/20 transition-colors duration-300">
                <Icon
                  name={stat.iconName}
                  className="w-10 h-10"
                  color="#4B935E"
                />
              </div>

              {/* Large, Visually Prominent Numbers */}
              <div className="mb-4">
                <div className="text-4xl sm:text-5xl lg:text-6xl font-black text-[#4B935E] mb-4 group-hover:scale-110 transition-transform duration-300 leading-none">
                  {stat.number}
                </div>
                <h3 className="text-xl sm:text-2xl font-bold text-[#2C3E50]">
                  {stat.label}
                </h3>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
