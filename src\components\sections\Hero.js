"use client";

import Image from "next/image";
import IslamicPattern from "../patterns/IslamicPattern";
import { useTranslations, useLocale } from "next-intl";

export default function Hero() {
  const t = useTranslations("hero");
  const locale = useLocale();
  const isRTL = locale === "ar";
  return (
    <section className="relative min-h-screen bg-gradient-to-br from-[#FFFEFB] via-[#F8F9FA] to-[#E8F5E8] overflow-hidden">
      <div className="absolute bottom-0 left-0 right-0 h-24 bg-[#E8F5E8]"></div>
      <IslamicPattern opacity={0.05} />

      {/* Main partie */}
      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 min-h-screen flex items-center pb-16 sm:pb-20 lg:pb-24">
        <div
          className={`grid lg:grid-cols-2 gap-8 lg:gap-12 items-center w-full ${
            isRTL ? "lg:grid-flow-col-dense" : ""
          }`}
        >
          {/* Text Content */}
          <div
            className={`space-y-6 sm:space-y-8 text-center lg:text-${
              isRTL ? "right" : "left"
            } ${isRTL ? "lg:order-2" : ""}`}
          >
            {/* el Logo */}
            <div
              className={`flex justify-center lg:justify-${
                isRTL ? "end" : "start"
              } mb-6 sm:mb-8`}
            >
              <div className="relative">
                <div className="absolute inset-0 bg-[#4B935E] rounded-full blur-xl opacity-20 scale-110"></div>
                <div className="relative bg-white rounded-full p-4 sm:p-6 shadow-2xl border-4 border-[#4B935E]/20">
                  <Image
                    src="/LOGO/logo.svg"
                    alt="AL-Insan Logo"
                    width={60}
                    height={90}
                    className="filter brightness-0 saturate-100 sm:w-[80px] sm:h-[120px]"
                    style={{
                      filter:
                        "invert(29%) sepia(15%) saturate(1729%) hue-rotate(152deg) brightness(91%) contrast(89%)",
                    }}
                  />
                </div>
              </div>
            </div>

            <div className="space-y-4 sm:space-y-6">
              <div className="text-center lg:text-left">
                <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold text-[#2C3E50] mb-4 leading-tight">
                  {t("title")}
                </h1>

                <p className="text-base sm:text-lg lg:text-xl text-[#7F8C8D] leading-relaxed max-w-2xl font-light mx-auto lg:mx-0">
                  {t("description")}
                </p>
              </div>
            </div>

            <div
              className={`flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center lg:justify-${
                isRTL ? "end" : "start"
              } pt-6 sm:pt-8`}
            >
              <button className="group relative px-6 sm:px-8 py-3 sm:py-4 bg-[#4B935E] text-white font-semibold text-base sm:text-lg transition-all duration-300 hover:bg-[#3A7A4A] hover:scale-105 hover:shadow-2xl overflow-hidden">
                {/* border */}
                <div className="absolute inset-0 border-2 border-[#D4AF37]/30 rounded-lg"></div>
                <div className="absolute top-1 left-1 right-1 bottom-1 border border-[#D4AF37]/20 rounded-md"></div>

                <div className="absolute top-2 left-2 w-3 h-3 border-l-2 border-t-2 border-[#D4AF37]/50"></div>
                <div className="absolute top-2 right-2 w-3 h-3 border-r-2 border-t-2 border-[#D4AF37]/50"></div>
                <div className="absolute bottom-2 left-2 w-3 h-3 border-l-2 border-b-2 border-[#D4AF37]/50"></div>
                <div className="absolute bottom-2 right-2 w-3 h-3 border-r-2 border-b-2 border-[#D4AF37]/50"></div>

                <span className="relative z-10 flex items-center gap-2">
                  <svg
                    className="w-5 h-5"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  {t("discoverButton")}
                </span>
                <div className="absolute inset-0 bg-gradient-to-r from-[#4B935E] to-[#D4AF37] opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg"></div>
              </button>

              <button className="group relative px-6 sm:px-8 py-3 sm:py-4 border-2 border-[#4B935E] text-[#4B935E] font-semibold text-base sm:text-lg transition-all duration-300 hover:bg-[#4B935E] hover:text-white hover:scale-105 hover:shadow-xl overflow-hidden">
                <div className="absolute inset-0 opacity-10 group-hover:opacity-20 transition-opacity duration-300">
                  <svg
                    className="w-full h-full"
                    viewBox="0 0 100 100"
                    fill="currentColor"
                  >
                    <pattern
                      id="islamic-btn-pattern"
                      x="0"
                      y="0"
                      width="20"
                      height="20"
                      patternUnits="userSpaceOnUse"
                    >
                      <path
                        d="M10,0 L15,5 L10,10 L5,5 Z"
                        fill="currentColor"
                        opacity="0.3"
                      />
                    </pattern>
                    <rect
                      width="100%"
                      height="100%"
                      fill="url(#islamic-btn-pattern)"
                    />
                  </svg>
                </div>

                <div className="absolute top-1 left-1 w-4 h-4">
                  <svg
                    viewBox="0 0 16 16"
                    className="w-full h-full text-[#D4AF37]"
                  >
                    <path
                      d="M0,8 Q0,0 8,0 Q8,8 0,8"
                      fill="currentColor"
                      opacity="0.6"
                    />
                  </svg>
                </div>
                <div className="absolute top-1 right-1 w-4 h-4 rotate-90">
                  <svg
                    viewBox="0 0 16 16"
                    className="w-full h-full text-[#D4AF37]"
                  >
                    <path
                      d="M0,8 Q0,0 8,0 Q8,8 0,8"
                      fill="currentColor"
                      opacity="0.6"
                    />
                  </svg>
                </div>
                <div className="absolute bottom-1 left-1 w-4 h-4 -rotate-90">
                  <svg
                    viewBox="0 0 16 16"
                    className="w-full h-full text-[#D4AF37]"
                  >
                    <path
                      d="M0,8 Q0,0 8,0 Q8,8 0,8"
                      fill="currentColor"
                      opacity="0.6"
                    />
                  </svg>
                </div>
                <div className="absolute bottom-1 right-1 w-4 h-4 rotate-180">
                  <svg
                    viewBox="0 0 16 16"
                    className="w-full h-full text-[#D4AF37]"
                  >
                    <path
                      d="M0,8 Q0,0 8,0 Q8,8 0,8"
                      fill="currentColor"
                      opacity="0.6"
                    />
                  </svg>
                </div>

                <span className="relative z-10 flex items-center justify-center gap-2">
                  <svg
                    className="w-5 h-5 transition-transform group-hover:scale-110"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                    />
                  </svg>
                  {t("donateButton")}
                </span>
              </button>
            </div>
          </div>

          {/* Geometric Design - Hidden on mobile, visible on large screens */}
          <div
            className={`hidden lg:flex relative justify-center lg:justify-${
              isRTL ? "start" : "end"
            } ${isRTL ? "lg:order-1" : ""}`}
          >
            <div className="relative w-96 h-96 lg:w-[500px] lg:h-[500px]">
              <div className="absolute inset-0 rounded-full bg-gradient-to-br from-[#4B935E]/20 to-[#4B935E]/10 backdrop-blur-sm border border-[#4B935E]/30"></div>

              <div className="absolute inset-4 rounded-full border-4 border-[#4B935E]/40 animate-spin-slow"></div>

              <div className="absolute inset-8 rounded-full bg-white/80 backdrop-blur-sm shadow-2xl flex items-center justify-center">
                <div className="relative w-full h-full">
                  <IslamicPattern opacity={0.3} className="text-[#4B935E]" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
