import IslamicPattern from "../patterns/IslamicPattern";

export default function UrgencyCTA() {
  return (
    <section className="relative py-8 sm:py-12 lg:py-16 bg-gradient-to-b from-[#E8F5E8] via-[#2C3E50] to-[#1A252F] overflow-hidden">
      {/* Islamic geometric border decorations */}
      <div className="absolute top-0 left-0 right-0 h-2 bg-gradient-to-r from-transparent via-[#4B935E] to-transparent"></div>
      <div className="absolute bottom-0 left-0 right-0 h-2 bg-gradient-to-r from-transparent via-[#4B935E] to-transparent"></div>
      {/* Islamic Pattern Background */}
      <div className="absolute inset-0">
        <IslamicPattern opacity={0.05} className="text-[#4B935E]" />
      </div>

      {/* Islamic Geometric Corner Decorations */}
      <div className="absolute top-4 left-4 w-16 h-16 opacity-30">
        <svg viewBox="0 0 64 64" className="w-full h-full text-[#4B935E]">
          <path d="M0,32 Q0,0 32,0 Q32,32 0,32" fill="currentColor" />
          <path
            d="M8,32 Q8,8 32,8 Q32,32 8,32"
            fill="none"
            stroke="currentColor"
            strokeWidth="1"
            opacity="0.5"
          />
        </svg>
      </div>
      <div className="absolute top-4 right-4 w-16 h-16 opacity-30 rotate-90">
        <svg viewBox="0 0 64 64" className="w-full h-full text-[#4B935E]">
          <path d="M0,32 Q0,0 32,0 Q32,32 0,32" fill="currentColor" />
          <path
            d="M8,32 Q8,8 32,8 Q32,32 8,32"
            fill="none"
            stroke="currentColor"
            strokeWidth="1"
            opacity="0.5"
          />
        </svg>
      </div>
      <div className="absolute bottom-4 left-4 w-16 h-16 opacity-30 -rotate-90">
        <svg viewBox="0 0 64 64" className="w-full h-full text-[#4B935E]">
          <path d="M0,32 Q0,0 32,0 Q32,32 0,32" fill="currentColor" />
          <path
            d="M8,32 Q8,8 32,8 Q32,32 8,32"
            fill="none"
            stroke="currentColor"
            strokeWidth="1"
            opacity="0.5"
          />
        </svg>
      </div>
      <div className="absolute bottom-4 right-4 w-16 h-16 opacity-30 rotate-180">
        <svg viewBox="0 0 64 64" className="w-full h-full text-[#4B935E]">
          <path d="M0,32 Q0,0 32,0 Q32,32 0,32" fill="currentColor" />
          <path
            d="M8,32 Q8,8 32,8 Q32,32 8,32"
            fill="none"
            stroke="currentColor"
            strokeWidth="1"
            opacity="0.5"
          />
        </svg>
      </div>

      {/* Central Islamic Geometric Star */}
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 opacity-10">
        <svg viewBox="0 0 128 128" className="w-full h-full text-[#4B935E]">
          <path
            d="M64 16 L76 52 L112 64 L76 76 L64 112 L52 76 L16 64 L52 52 Z"
            fill="currentColor"
          />
          <circle
            cx="64"
            cy="64"
            r="20"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
          />
        </svg>
      </div>

      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          {/* Urgent Call to Action Header */}
          <div className="mb-6 sm:mb-8">
            <div className="inline-flex items-center justify-center px-4 py-2 bg-red-500/20 border border-red-400/30 rounded-full mb-4">
              <svg
                className="w-5 h-5 text-red-300 mr-2"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
              <span className="text-red-200 font-semibold text-sm uppercase tracking-wide">
                Urgent Need
              </span>
            </div>

            <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-4 leading-tight">
              Help Us Save Lives
              <span className="block text-3xl sm:text-4xl lg:text-5xl text-[#E8F5E8] mt-2">
                Today
              </span>
            </h2>

            <div className="w-32 h-1 bg-[#4B935E] mx-auto rounded-full mb-6"></div>
          </div>

          {/* Urgent Needs Description */}
          <div className="mb-8 sm:mb-10">
            <p className="text-xl sm:text-2xl text-[#E8F5E8] leading-relaxed font-light max-w-3xl mx-auto mb-3">
              Thousands of families in Algeria are facing severe food shortages
              and lack access to clean water this winter.
            </p>
            <p className="text-lg sm:text-xl text-white/80 leading-relaxed max-w-2xl mx-auto">
              Your immediate donation can provide emergency food packages and
              clean water to families who desperately need help right now.
            </p>
          </div>

          {/* Hero-style Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center items-center">
            {/* Discover Our Actions Button */}
            <button className="group relative px-6 sm:px-8 py-3 sm:py-4 bg-[#4B935E] text-white font-semibold text-base sm:text-lg rounded-xl transition-all duration-300 hover:bg-[#3A7A4A] hover:scale-105 hover:shadow-2xl overflow-hidden">
              <div className="absolute inset-0 opacity-10 group-hover:opacity-20 transition-opacity duration-300">
                <svg
                  className="w-full h-full"
                  viewBox="0 0 100 100"
                  fill="currentColor"
                >
                  <pattern
                    id="hero-btn-pattern-1"
                    x="0"
                    y="0"
                    width="20"
                    height="20"
                    patternUnits="userSpaceOnUse"
                  >
                    <path
                      d="M10,0 L15,5 L10,10 L5,5 Z"
                      fill="currentColor"
                      opacity="0.3"
                    />
                  </pattern>
                  <rect
                    width="100%"
                    height="100%"
                    fill="url(#hero-btn-pattern-1)"
                  />
                </svg>
              </div>

              <div className="absolute top-2 left-2 w-4 h-4">
                <svg
                  viewBox="0 0 16 16"
                  className="w-full h-full text-white opacity-20"
                >
                  <path
                    d="M0,8 Q0,0 8,0 Q8,8 0,8"
                    fill="currentColor"
                    opacity="0.6"
                  />
                </svg>
              </div>
              <div className="absolute top-2 right-2 w-4 h-4 rotate-90">
                <svg
                  viewBox="0 0 16 16"
                  className="w-full h-full text-white opacity-20"
                >
                  <path
                    d="M0,8 Q0,0 8,0 Q8,8 0,8"
                    fill="currentColor"
                    opacity="0.6"
                  />
                </svg>
              </div>
              <div className="absolute bottom-2 left-2 w-4 h-4 -rotate-90">
                <svg
                  viewBox="0 0 16 16"
                  className="w-full h-full text-white opacity-20"
                >
                  <path
                    d="M0,8 Q0,0 8,0 Q8,8 0,8"
                    fill="currentColor"
                    opacity="0.6"
                  />
                </svg>
              </div>
              <div className="absolute bottom-2 right-2 w-4 h-4 rotate-180">
                <svg
                  viewBox="0 0 16 16"
                  className="w-full h-full text-white opacity-20"
                >
                  <path
                    d="M0,8 Q0,0 8,0 Q8,8 0,8"
                    fill="currentColor"
                    opacity="0.6"
                  />
                </svg>
              </div>

              <span className="relative z-10 flex items-center justify-center gap-2">
                <svg
                  className="w-5 h-5 transition-transform group-hover:scale-110"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Discover Our Actions
              </span>
            </button>

            {/* Donate Now Button */}
            <button className="group relative px-6 sm:px-8 py-3 sm:py-4 border-2 border-[#4B935E] text-[#4B935E] font-semibold text-base sm:text-lg rounded-xl transition-all duration-300 hover:bg-[#4B935E] hover:text-white hover:scale-105 hover:shadow-xl overflow-hidden">
              <div className="absolute inset-0 opacity-10 group-hover:opacity-20 transition-opacity duration-300">
                <svg
                  className="w-full h-full"
                  viewBox="0 0 100 100"
                  fill="currentColor"
                >
                  <pattern
                    id="hero-btn-pattern-2"
                    x="0"
                    y="0"
                    width="20"
                    height="20"
                    patternUnits="userSpaceOnUse"
                  >
                    <path
                      d="M10,0 L15,5 L10,10 L5,5 Z"
                      fill="currentColor"
                      opacity="0.3"
                    />
                  </pattern>
                  <rect
                    width="100%"
                    height="100%"
                    fill="url(#hero-btn-pattern-2)"
                  />
                </svg>
              </div>

              <div className="absolute top-2 left-2 w-4 h-4">
                <svg
                  viewBox="0 0 16 16"
                  className="w-full h-full text-[#4B935E] group-hover:text-white opacity-40"
                >
                  <path
                    d="M0,8 Q0,0 8,0 Q8,8 0,8"
                    fill="currentColor"
                    opacity="0.6"
                  />
                </svg>
              </div>
              <div className="absolute top-2 right-2 w-4 h-4 rotate-90">
                <svg
                  viewBox="0 0 16 16"
                  className="w-full h-full text-[#4B935E] group-hover:text-white opacity-40"
                >
                  <path
                    d="M0,8 Q0,0 8,0 Q8,8 0,8"
                    fill="currentColor"
                    opacity="0.6"
                  />
                </svg>
              </div>
              <div className="absolute bottom-2 left-2 w-4 h-4 -rotate-90">
                <svg
                  viewBox="0 0 16 16"
                  className="w-full h-full text-[#4B935E] group-hover:text-white opacity-40"
                >
                  <path
                    d="M0,8 Q0,0 8,0 Q8,8 0,8"
                    fill="currentColor"
                    opacity="0.6"
                  />
                </svg>
              </div>
              <div className="absolute bottom-2 right-2 w-4 h-4 rotate-180">
                <svg
                  viewBox="0 0 16 16"
                  className="w-full h-full text-[#4B935E] group-hover:text-white opacity-40"
                >
                  <path
                    d="M0,8 Q0,0 8,0 Q8,8 0,8"
                    fill="currentColor"
                    opacity="0.6"
                  />
                </svg>
              </div>

              <span className="relative z-10 flex items-center justify-center gap-2">
                <svg
                  className="w-5 h-5 transition-transform group-hover:scale-110"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                  />
                </svg>
                Donate Now
              </span>
            </button>
          </div>

          {/* Trust Indicators */}
          <div className="mt-8 sm:mt-10">
            <div className="flex flex-col sm:flex-row items-center justify-center gap-8 text-white/70">
              <div className="flex items-center gap-2">
                <svg
                  className="w-5 h-5 text-[#4B935E]"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clipRule="evenodd"
                  />
                </svg>
                <span className="text-sm font-medium">
                  100% Secure Donations
                </span>
              </div>
              <div className="flex items-center gap-2">
                <svg
                  className="w-5 h-5 text-[#4B935E]"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="text-sm font-medium">Verified Impact</span>
              </div>
              <div className="flex items-center gap-2">
                <svg
                  className="w-5 h-5 text-[#4B935E]"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clipRule="evenodd"
                  />
                </svg>
                <span className="text-sm font-medium">Direct to Families</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
