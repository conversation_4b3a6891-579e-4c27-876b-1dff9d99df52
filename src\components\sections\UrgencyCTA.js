import IslamicPattern from "../patterns/IslamicPattern";

export default function UrgencyCTA() {
  return (
    <section className="relative py-16 sm:py-20 lg:py-24 bg-gradient-to-br from-[#2C3E50] via-[#34495E] to-[#4B935E] overflow-hidden">
      {/* Islamic Pattern Background */}
      <div className="absolute inset-0">
        <IslamicPattern opacity={0.08} className="text-white" />
      </div>

      {/* Unique Islamic Geometric Elements */}
      <div className="absolute top-0 left-0 w-full h-full pointer-events-none">
        {/* Large geometric star - top left */}
        <div className="absolute top-8 left-8 w-32 h-32 opacity-10">
          <svg viewBox="0 0 128 128" className="w-full h-full text-white">
            <path d="M64 0 L80 48 L128 64 L80 80 L64 128 L48 80 L0 64 L48 48 Z" fill="currentColor"/>
          </svg>
        </div>
        
        {/* Medium geometric pattern - top right */}
        <div className="absolute top-12 right-12 w-24 h-24 opacity-15">
          <svg viewBox="0 0 96 96" className="w-full h-full text-white">
            <g transform="translate(48,48)">
              <path d="M0,-30 L15,-15 L30,0 L15,15 L0,30 L-15,15 L-30,0 L-15,-15 Z" fill="currentColor"/>
            </g>
          </svg>
        </div>

        {/* Small geometric elements - bottom */}
        <div className="absolute bottom-8 left-1/4 w-16 h-16 opacity-20">
          <svg viewBox="0 0 64 64" className="w-full h-full text-white">
            <circle cx="32" cy="32" r="20" fill="none" stroke="currentColor" strokeWidth="2"/>
            <path d="M32,12 L32,52 M12,32 L52,32" stroke="currentColor" strokeWidth="1"/>
          </svg>
        </div>
        
        <div className="absolute bottom-12 right-1/3 w-20 h-20 opacity-15">
          <svg viewBox="0 0 80 80" className="w-full h-full text-white">
            <path d="M40,10 L50,30 L70,40 L50,50 L40,70 L30,50 L10,40 L30,30 Z" fill="currentColor"/>
          </svg>
        </div>
      </div>

      <div className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          
          {/* Urgent Call to Action Header */}
          <div className="mb-8 sm:mb-12">
            <div className="inline-flex items-center justify-center px-4 py-2 bg-red-500/20 border border-red-400/30 rounded-full mb-6">
              <svg className="w-5 h-5 text-red-300 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <span className="text-red-200 font-semibold text-sm uppercase tracking-wide">Urgent Need</span>
            </div>
            
            <h2 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
              Help Us Save Lives
              <span className="block text-3xl sm:text-4xl lg:text-5xl text-[#E8F5E8] mt-2">
                Today
              </span>
            </h2>
            
            <div className="w-32 h-1 bg-[#4B935E] mx-auto rounded-full mb-8"></div>
          </div>

          {/* Urgent Needs Description */}
          <div className="mb-12 sm:mb-16">
            <p className="text-xl sm:text-2xl text-[#E8F5E8] leading-relaxed font-light max-w-3xl mx-auto mb-4">
              Thousands of families in Algeria are facing severe food shortages and lack access to clean water this winter.
            </p>
            <p className="text-lg sm:text-xl text-white/80 leading-relaxed max-w-2xl mx-auto">
              Your immediate donation can provide emergency food packages and clean water to families who desperately need help right now.
            </p>
          </div>

          {/* Prominent Donate Now Button */}
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
            <button className="group relative px-10 py-5 bg-[#4B935E] text-white font-bold text-xl rounded-2xl transition-all duration-300 hover:bg-[#3A7A4A] hover:scale-110 hover:shadow-2xl overflow-hidden min-w-[280px]">
              {/* Islamic geometric border */}
              <div className="absolute inset-0 border-3 border-[#4B935E]/40 rounded-2xl"></div>
              <div className="absolute top-2 left-2 right-2 bottom-2 border-2 border-[#4B935E]/20 rounded-xl"></div>
              
              {/* Geometric pattern overlay */}
              <div className="absolute inset-0 opacity-20 group-hover:opacity-30 transition-opacity duration-300">
                <svg className="w-full h-full" viewBox="0 0 100 100" fill="currentColor">
                  <pattern id="urgent-btn-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
                    <path d="M10,0 L15,5 L10,10 L5,5 Z" fill="currentColor" opacity="0.3"/>
                  </pattern>
                  <rect width="100%" height="100%" fill="url(#urgent-btn-pattern)"/>
                </svg>
              </div>
              
              <span className="relative z-10 flex items-center justify-center gap-3">
                <svg className="w-6 h-6 transition-transform group-hover:scale-125" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
                Donate Now - Save Lives
              </span>
            </button>

            {/* Secondary Action */}
            <button className="group px-8 py-4 border-2 border-white/30 text-white font-semibold text-lg rounded-xl transition-all duration-300 hover:bg-white/10 hover:border-white/50 hover:scale-105">
              <span className="flex items-center justify-center gap-2">
                <svg className="w-5 h-5 transition-transform group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Learn More
              </span>
            </button>
          </div>

          {/* Trust Indicators */}
          <div className="mt-12 sm:mt-16">
            <div className="flex flex-col sm:flex-row items-center justify-center gap-8 text-white/70">
              <div className="flex items-center gap-2">
                <svg className="w-5 h-5 text-[#4B935E]" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-sm font-medium">100% Secure Donations</span>
              </div>
              <div className="flex items-center gap-2">
                <svg className="w-5 h-5 text-[#4B935E]" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                <span className="text-sm font-medium">Verified Impact</span>
              </div>
              <div className="flex items-center gap-2">
                <svg className="w-5 h-5 text-[#4B935E]" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span className="text-sm font-medium">Direct to Families</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
