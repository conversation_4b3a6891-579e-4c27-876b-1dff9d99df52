


import React from 'react';
import { HeroPattern } from './IslamicPattern';

/**
 * Hero Section Component
 * 
 * @param {Object} props - Component props
 * @param {Function} props.onDonateClick - Callback for donate button click
 */
const HeroSection = ({ onDonateClick }) => {
  return (
    <section 
      className="relative min-h-screen flex items-center justify-center overflow-hidden"
      aria-labelledby="hero-title"
    >
      {/* Islamic Geometric Background Pattern */}
      <HeroPattern className="absolute inset-0">
        {/* Gradient overlay for better text readability */}
        <div className="absolute inset-0 bg-gradient-to-br from-background/95 via-background/90 to-primary/5" />
      </HeroPattern>

      {/* Main Content Container */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
        <div className="text-center space-y-8">
          
          {/* Arabic Bismillah */}
          <div className="mb-8">
            <p 
              className="text-2xl sm:text-3xl font-arabic text-primary/80 mb-2"
              lang="ar"
              dir="rtl"
            >
              بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيم
            </p>
            <p className="text-sm text-muted italic">
              In the name of Allah, the Most Gracious, the Most Merciful
            </p>
          </div>

          {/* Main Headline */}
          <div className="space-y-4">
            <h1 
              id="hero-title"
              className="text-4xl sm:text-5xl lg:text-7xl font-bold text-foreground leading-tight"
            >
              <span className="block">Transform Lives</span>
              <span className="block text-primary">Through Compassion</span>
            </h1>
            
            {/* Decorative Islamic Element */}
            <div className="flex justify-center items-center space-x-4 my-6">
              <div className="w-16 h-px bg-gradient-to-r from-transparent to-primary"></div>
              <div className="w-2 h-2 bg-primary rounded-full"></div>
              <div className="w-8 h-px bg-primary"></div>
              <div className="w-2 h-2 bg-primary rounded-full"></div>
              <div className="w-16 h-px bg-gradient-to-l from-transparent to-primary"></div>
            </div>
          </div>

          {/* Mission Statement */}
          <div className="max-w-3xl mx-auto space-y-4">
            <p className="text-lg sm:text-xl lg:text-2xl text-muted leading-relaxed">
              Join us in making a meaningful difference in the lives of those in need. 
              Through the blessed tradition of Eid sacrifice and humanitarian aid, 
              we bring hope and sustenance to communities across Algeria and beyond.
            </p>
            
            <p className="text-base sm:text-lg text-muted/80 max-w-2xl mx-auto">
              Every donation is a step towards building a more compassionate world, 
              guided by Islamic values of charity, kindness, and community support.
            </p>
          </div>

          {/* Call-to-Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mt-12">
            {/* Primary CTA - Eid Sacrifice Donation */}
            <button
              onClick={onDonateClick}
              className="group relative px-8 py-4 bg-primary hover:bg-primary-dark text-white font-semibold text-lg rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-primary/30 focus:ring-offset-2"
              aria-label="Donate for Eid Sacrifice - Primary donation option"
            >
              <span className="relative z-10 flex items-center space-x-2">
                <span>🐑</span>
                <span>Donate for Eid Sacrifice</span>
                <svg 
                  className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                  aria-hidden="true"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </span>
              
              {/* Button glow effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-primary-light to-primary rounded-xl opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
            </button>

            {/* Secondary CTA - Learn More */}
            <button
              onClick={() => document.getElementById('about-section')?.scrollIntoView({ behavior: 'smooth' })}
              className="px-8 py-4 border-2 border-primary text-primary hover:bg-primary hover:text-white font-semibold text-lg rounded-xl transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-primary/30 focus:ring-offset-2"
              aria-label="Learn more about our mission and impact"
            >
              Learn Our Mission
            </button>
          </div>

          {/* Trust Indicators */}
          <div className="mt-16 pt-8 border-t border-accent/50">
            <div className="flex flex-col sm:flex-row justify-center items-center space-y-4 sm:space-y-0 sm:space-x-8 text-sm text-muted">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-success rounded-full"></div>
                <span>Trusted by 1000+ donors</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-success rounded-full"></div>
                <span>100% transparent donations</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-success rounded-full"></div>
                <span>Islamic principles guided</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-primary rounded-full flex justify-center">
          <div className="w-1 h-3 bg-primary rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
