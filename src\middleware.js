import createMiddleware from 'next-intl/middleware';

export default createMiddleware({
  // A list of all locales that are supported
  locales: ['en', 'ar', 'fr'],

  // Used when no locale matches
  defaultLocale: 'en',

  // Always show locale in URL for Arabic and French
  localePrefix: {
    mode: 'as-needed',
    prefixes: {
      'ar': '/ar',
      'fr': '/fr'
    }
  }
});

export const config = {
  // Match only internationalized pathnames
  matcher: ['/', '/(ar|fr)/:path*']
};
