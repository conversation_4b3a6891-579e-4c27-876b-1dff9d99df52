import { defineRouting } from 'next-intl/routing';

export const routing = defineRouting({
  // A list of all locales that are supported
  locales: ['en', 'ar', 'fr'],

  // Used when no locale matches
  defaultLocale: 'en',

  // The `pathnames` object holds pairs of internal and
  // external paths. Based on the locale, the external
  // paths are rewritten to the shared, internal ones.
  pathnames: {
    // If all locales use the same pathname, a single
    // external path can be provided to `pathnames`
    '/': '/',
    
    // Alternatively, you can use a nested object to
    // provide external paths for each locale
    // '/about': {
    //   en: '/about',
    //   ar: '/حول',
    //   fr: '/a-propos'
    // }
  }
});
