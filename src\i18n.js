import { notFound } from "next/navigation";
import { getRequestConfig } from "next-intl/server";
import { getRequestLocale } from "next-intl/server";

// Can be imported from a shared config
const locales = ["en", "ar", "fr"];

export default getRequestConfig(async () => {
  // Get the locale from the request
  const locale = await getRequestLocale();

  // Validate that the incoming `locale` parameter is valid
  if (!locale || !locales.includes(locale)) notFound();

  return {
    locale,
    messages: (await import(`../messages/${locale}.json`)).default,
  };
});
