@tailwind base;
@tailwind components;
@tailwind utilities;

/* Arabic Font Definition */
.font-arabic {
  font-family: 'Baloo Bhaijaan 2', 'Poppins', sans-serif;
}

/* RTL Layout Utilities */
@layer utilities {
  /* RTL-specific utilities */
  .rtl\:text-right:where([dir="rtl"], [dir="rtl"] *) {
    text-align: right;
  }
  
  .rtl\:text-left:where([dir="rtl"], [dir="rtl"] *) {
    text-align: left;
  }
  
  .rtl\:flex-row-reverse:where([dir="rtl"], [dir="rtl"] *) {
    flex-direction: row-reverse;
  }
  
  .rtl\:space-x-reverse:where([dir="rtl"], [dir="rtl"] *) > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
  }
  
  /* RTL Grid utilities */
  .rtl\:grid-cols-reverse:where([dir="rtl"], [dir="rtl"] *) {
    direction: rtl;
  }
  
  /* RTL positioning */
  .rtl\:right-4:where([dir="rtl"], [dir="rtl"] *) {
    right: 1rem;
    left: auto;
  }
  
  .rtl\:left-4:where([dir="rtl"], [dir="rtl"] *) {
    left: 1rem;
    right: auto;
  }
  
  /* RTL margin utilities */
  .rtl\:mr-2:where([dir="rtl"], [dir="rtl"] *) {
    margin-right: 0.5rem;
    margin-left: 0;
  }
  
  .rtl\:ml-2:where([dir="rtl"], [dir="rtl"] *) {
    margin-left: 0.5rem;
    margin-right: 0;
  }
  
  .rtl\:mr-4:where([dir="rtl"], [dir="rtl"] *) {
    margin-right: 1rem;
    margin-left: 0;
  }
  
  .rtl\:ml-4:where([dir="rtl"], [dir="rtl"] *) {
    margin-left: 1rem;
    margin-right: 0;
  }
}

/* Base RTL Support */
[dir="rtl"] {
  direction: rtl;
}

[dir="rtl"] .grid {
  direction: rtl;
}

[dir="rtl"] .ltr-content {
  direction: ltr;
  display: inline-block;
}

[dir="rtl"] {
  font-family: 'Baloo Bhaijaan 2', 'Poppins', sans-serif;
}

[dir="rtl"] .geometric-pattern {
  transform: none !important;
}

[dir="rtl"] .btn-icon-left {
  flex-direction: row-reverse;
}

[dir="rtl"] .btn-icon-right {
  flex-direction: row;
}

/* Language switcher positioning */
[dir="rtl"] .language-switcher {
  left: 1rem;
  right: auto;
}

[dir="ltr"] .language-switcher {
  right: 1rem;
  left: auto;
}

/* Mixed content handling */
.mixed-content {
  unicode-bidi: embed;
}

/* Ensure proper Arabic text rendering */
[lang="ar"] {
  font-feature-settings: "liga" 1, "calt" 1;
  text-rendering: optimizeLegibility;
}

/* Islamic phrase special styling */
.islamic-phrase {
  font-family: 'Baloo Bhaijaan 2', serif;
  font-weight: 600;
  letter-spacing: 0.02em;
}

/* RTL flex utilities */
[dir="rtl"] .flex-row {
  flex-direction: row-reverse;
}

[dir="rtl"] .justify-start {
  justify-content: flex-end;
}

[dir="rtl"] .justify-end {
  justify-content: flex-start;
}

/* RTL text alignment */
[dir="rtl"] .text-left {
  text-align: right;
}

[dir="rtl"] .text-right {
  text-align: left;
}

/* Preserve center alignment */
[dir="rtl"] .text-center {
  text-align: center;
}

/* RTL-specific animations and transforms */
[dir="rtl"] .transform {
  transform: scaleX(-1);
}

[dir="rtl"] .no-flip {
  transform: none !important;
}

/* Ensure icons and geometric patterns don't flip inappropriately */
[dir="rtl"] svg.no-flip,
[dir="rtl"] .geometric-decoration,
[dir="rtl"] .islamic-pattern {
  transform: none !important;
}

/* RTL-specific spacing */
[dir="rtl"] .space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}

[dir="rtl"] .space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

/* Ensure proper number display in RTL */
[dir="rtl"] .number-display {
  direction: ltr;
  display: inline-block;
  unicode-bidi: embed;
}
