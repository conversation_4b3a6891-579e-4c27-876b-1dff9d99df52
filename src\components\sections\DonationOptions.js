import DonationCard from "../ui/DonationCard";

export default function DonationOptions() {
  const donationOptions = [
    {
      id: 1,
      title: "Eid Sacrifice",
      description:
        "Provide a sacrificial animal for families in need during Eid celebrations, following Islamic traditions.",
      price: 20000,
      isAvailable: true,
      iconName: "sacrifice",
    },
    {
      id: 2,

      title: "Ramadan Food Baskets",

      description:
        "Essential food packages for families to break their fast during the holy month of Ramadan.",

      price: 2000,

      isAvailable: false,

      iconName: "food-basket",

      comingSoonDate: "Ramadan 2025",
    },

    {
      id: 3,

      title: "Clean Water Initiatives",

      description:
        "Support clean water projects including wells and water purification systems for communities.",

      price: 150000,

      isAvailable: false,

      iconName: "water",

      comingSoonDate: "Q2 2025",
    },

    {
      id: 4,

      title: "Waqf Equipment",

      description:
        "Contribute to Islamic endowment equipment for educational and community development projects.",

      price: 7000,

      isAvailable: false,

      iconName: "equipment",

      comingSoonDate: "Q3 2025",
    },

    {
      id: 5,

      title: "Mosque Building",

      description:
        "Support the construction and renovation of mosques to serve growing Muslim communities.",

      price: 200000,

      isAvailable: false,

      iconName: "mosque",

      comingSoonDate: "Q4 2025",
    },
  ];

  return (
    <section className="pt-0 pb-16 sm:pb-20 lg:pb-24 bg-gradient-to-b from-[#E8F5E8] via-[#F8F9FA] to-[#FFFEFB]">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-[#2C3E50] mb-4">
            Donation Options
          </h2>

          <p className="text-lg sm:text-xl text-[#7F8C8D] leading-relaxed max-w-3xl mx-auto">
            Choose from our Islamic charitable giving options. Support those in
            need through traditional Islamic practices and community development
            initiatives.
          </p>
        </div>
           
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 lg:gap-8">
          {donationOptions.map((option) => (
            <DonationCard
              key={option.id}
              title={option.title}
              description={option.description}
              price={option.price}
              isAvailable={option.isAvailable}
              iconName={option.iconName}
              comingSoonDate={option.comingSoonDate}
            />
          ))}
                 {" "}
        </div>
      </div>
    </section>
  );
}
