import Icon from "./Icon";

export default function DonationCard({
  title,
  description,
  price,
  isAvailable = true,
  iconName,
  comingSoonDate,
}) {
  return (
    <div
      className={`
      relative bg-white rounded-xl shadow-lg border-2 transition-all duration-300 overflow-hidden
      ${
        isAvailable
          ? "border-[#4B935E]/20 hover:border-[#4B935E]/40 hover:shadow-2xl hover:scale-105 cursor-pointer"
          : "border-gray-200 opacity-75 cursor-not-allowed"
      }
    `}
    >
      <div className="absolute top-2 left-2 w-6 h-6">
        <svg
          viewBox="0 0 24 24"
          className="w-full h-full text-[#4B935E] opacity-30"
        >
          <path d="M0,12 Q0,0 12,0 Q12,12 0,12" fill="currentColor" />
        </svg>
      </div>
      <div className="absolute top-2 right-2 w-6 h-6 rotate-90">
        <svg
          viewBox="0 0 24 24"
          className="w-full h-full text-[#4B935E] opacity-30"
        >
          <path d="M0,12 Q0,0 12,0 Q12,12 0,12" fill="currentColor" />
        </svg>
      </div>

      {!isAvailable && (
        <div className="absolute inset-0 bg-gray-50/70 backdrop-blur-[2px] z-10 flex items-center justify-center">
          <div className="text-center">
            <div className="w-12 h-12 mx-auto mb-3 bg-gray-400 rounded-full flex items-center justify-center">
              <svg
                className="w-6 h-6 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                />
              </svg>
            </div>
            <p className="text-gray-600 font-semibold text-sm">Coming Soon</p>
            {comingSoonDate && (
              <p className="text-gray-500 text-xs mt-1">{comingSoonDate}</p>
            )}
          </div>
        </div>
      )}

      <div className="p-6">
        <div className="w-16 h-16 mx-auto mb-4 bg-[#4B935E]/10 rounded-full flex items-center justify-center">
          <Icon name={iconName} className="w-8 h-8" color="#4B935E" />
        </div>

        <div className="text-center">
          <h3 className="text-xl font-bold text-[#2C3E50] mb-3">{title}</h3>
          <p className="text-[#7F8C8D] text-sm leading-relaxed mb-4">
            {description}
          </p>

          {isAvailable && price && (
            <div className="mb-4">
              <span className="text-2xl font-bold text-[#4B935E]">
                ${price}
              </span>
              <span className="text-[#7F8C8D] text-sm ml-1">per donation</span>
            </div>
          )}

          {isAvailable ? (
            <button className="w-full bg-[#4B935E] text-white py-3 px-4 rounded-lg font-semibold transition-all duration-300 hover:bg-[#3A7A4A] hover:shadow-lg">
              <span className="flex items-center justify-center gap-2">
                <svg
                  className="w-5 h-5 transition-transform hover:scale-110"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"
                  />
                </svg>
                Donate Now
              </span>
            </button>
          ) : (
            <button
              disabled
              className="w-full bg-gray-300 text-gray-500 py-3 px-4 rounded-lg font-semibold cursor-not-allowed"
            >
              Not Available Yet
            </button>
          )}
        </div>
      </div>

      <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-[#4B935E]/30 to-transparent"></div>
    </div>
  );
}
