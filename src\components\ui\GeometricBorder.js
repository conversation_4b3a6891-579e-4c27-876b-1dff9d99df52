export default function GeometricBorder({ position = "top-left" }) {
  const getPositionClasses = () => {
    switch (position) {
      case "top-left":
        return "top-0 left-0";
      case "top-right":
        return "top-0 right-0 rotate-90";
      case "bottom-left":
        return "bottom-20 left-0 -rotate-90";
      case "bottom-right":
        return "bottom-20 right-0 rotate-180";
      default:
        return "top-0 left-0";
    }
  };

  return (
    <div
      className={`absolute ${getPositionClasses()} w-32 h-32 pointer-events-none`}
    >
      <svg
        className="w-full h-full text-[#4B935E]"
        viewBox="0 0 128 128"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        {/* Outer Corner Frame */}
        <path
          d="M0 0 L32 0 L32 4 L4 4 L4 32 L0 32 Z"
          fill="currentColor"
          fillOpacity="0.3"
        />

        {/* Inner Geometric Pattern */}
        <g transform="translate(8,8)">
          {/* Central Star */}
          <path
            d="M16 0 L20 12 L32 16 L20 20 L16 32 L12 20 L0 16 L12 12 Z"
            fill="currentColor"
            fillOpacity="0.4"
          />

          {/* Corner Decorations */}
          <circle cx="8" cy="8" r="2" fill="currentColor" fillOpacity="0.6" />
          <circle
            cx="24"
            cy="8"
            r="1.5"
            fill="currentColor"
            fillOpacity="0.5"
          />
          <circle
            cx="8"
            cy="24"
            r="1.5"
            fill="currentColor"
            fillOpacity="0.5"
          />

          {/* Connecting Lines */}
          <path
            d="M0 0 L8 8 M24 8 L32 0 M8 24 L0 32"
            stroke="currentColor"
            strokeWidth="1"
            strokeOpacity="0.4"
          />
        </g>

        {/* Decorative Border Lines */}
        <path
          d="M40 0 L48 0 M0 40 L0 48 M56 0 L64 0 M0 56 L0 64"
          stroke="currentColor"
          strokeWidth="3"
          strokeOpacity="0.6"
        />

        {/* Small Geometric Elements */}
        <g transform="translate(48,8)">
          <path d="M0 0 L8 0 L4 8 Z" fill="currentColor" fillOpacity="0.3" />
        </g>

        <g transform="translate(8,48)">
          <path d="M0 0 L8 4 L0 8 Z" fill="currentColor" fillOpacity="0.3" />
        </g>

        {/* Definitions */}
        <defs>
          <linearGradient
            id={`gradient-${position}`}
            x1="0%"
            y1="0%"
            x2="100%"
            y2="100%"
          >
            <stop offset="0%" stopColor="currentColor" stopOpacity="0.4" />
            <stop offset="100%" stopColor="currentColor" stopOpacity="0.1" />
          </linearGradient>
          <mask id={`corner-mask-${position}`}>
            <rect width="64" height="64" fill="white" />
            <path d="M32 0 L64 0 L64 32 Z" fill="black" />
          </mask>
        </defs>

        {/* Gradient Overlay */}
        <rect
          x="0"
          y="0"
          width="64"
          height="64"
          fill={`url(#gradient-${position})`}
          mask={`url(#corner-mask-${position})`}
        />
      </svg>
    </div>
  );
}
