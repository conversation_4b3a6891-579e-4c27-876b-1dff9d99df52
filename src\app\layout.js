import { Poppins, <PERSON><PERSON><PERSON>_Bhai<PERSON>_2 } from "next/font/google";
import "./globals.css";

const poppins = Poppins({
  variable: "--font-poppins",
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
});

const balooBhaijaan = Baloo_Bhaijaan_2({
  variable: "--font-baloo-bhaijaan",
  subsets: ["arabic"],
  weight: ["400", "500", "600", "700"],
});

export const metadata = {
  title: "AL-Insan - Algerian Human Organization",
  description: "Promoting human dignity and Islamic values in Algeria",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body
        className={`${poppins.variable} ${balooBhaijaan.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
