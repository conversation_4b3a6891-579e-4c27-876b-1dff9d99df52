'use client';

import { useLocale, useTranslations } from 'next-intl';
import { useRouter, usePathname } from 'next/navigation';
import { useState } from 'react';

export default function LanguageSwitcher() {
  const t = useTranslations('languageSwitcher');
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);

  const languages = [
    { code: 'ar', name: t('arabic'), flag: '🇩🇿' },
    { code: 'fr', name: t('french'), flag: '🇫🇷' },
    { code: 'en', name: t('english'), flag: '🇺🇸' }
  ];

  const currentLanguage = languages.find(lang => lang.code === locale);

  const handleLanguageChange = (newLocale) => {
    // Remove current locale from pathname
    const pathWithoutLocale = pathname.replace(`/${locale}`, '') || '/';
    
    // Add new locale to pathname
    const newPath = newLocale === 'en' ? pathWithoutLocale : `/${newLocale}${pathWithoutLocale}`;
    
    router.push(newPath);
    setIsOpen(false);
  };

  return (
    <div className="relative language-switcher">
      {/* Islamic geometric corner decorations */}
      <div className="absolute -top-2 -left-2 w-4 h-4 opacity-20">
        <svg viewBox="0 0 16 16" className="w-full h-full text-[#4B935E]">
          <path d="M0,8 Q0,0 8,0 Q8,8 0,8" fill="currentColor"/>
        </svg>
      </div>
      <div className="absolute -top-2 -right-2 w-4 h-4 opacity-20 rotate-90">
        <svg viewBox="0 0 16 16" className="w-full h-full text-[#4B935E]">
          <path d="M0,8 Q0,0 8,0 Q8,8 0,8" fill="currentColor"/>
        </svg>
      </div>
      <div className="absolute -bottom-2 -left-2 w-4 h-4 opacity-20 -rotate-90">
        <svg viewBox="0 0 16 16" className="w-full h-full text-[#4B935E]">
          <path d="M0,8 Q0,0 8,0 Q8,8 0,8" fill="currentColor"/>
        </svg>
      </div>
      <div className="absolute -bottom-2 -right-2 w-4 h-4 opacity-20 rotate-180">
        <svg viewBox="0 0 16 16" className="w-full h-full text-[#4B935E]">
          <path d="M0,8 Q0,0 8,0 Q8,8 0,8" fill="currentColor"/>
        </svg>
      </div>

      {/* Main switcher button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="group relative flex items-center gap-2 bg-white/90 backdrop-blur-sm border-2 border-[#4B935E]/20 rounded-lg px-3 py-2 shadow-lg transition-all duration-300 hover:bg-white hover:border-[#4B935E]/40 hover:shadow-xl"
      >
        {/* Islamic pattern background */}
        <div className="absolute inset-0 opacity-5 group-hover:opacity-10 transition-opacity duration-300">
          <svg className="w-full h-full" viewBox="0 0 100 100" fill="currentColor">
            <pattern id="lang-switcher-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
              <path d="M10,0 L15,5 L10,10 L5,5 Z" fill="#4B935E" opacity="0.3"/>
            </pattern>
            <rect width="100%" height="100%" fill="url(#lang-switcher-pattern)"/>
          </svg>
        </div>

        <span className="relative z-10 text-lg">{currentLanguage?.flag}</span>
        <span className="relative z-10 text-sm font-semibold text-[#2C3E50] group-hover:text-[#4B935E] transition-colors duration-300">
          {currentLanguage?.name}
        </span>
        
        {/* Dropdown arrow */}
        <svg 
          className={`relative z-10 w-4 h-4 text-[#4B935E] transition-transform duration-300 ${isOpen ? 'rotate-180' : ''}`}
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* Dropdown menu */}
      {isOpen && (
        <div className="absolute top-full mt-2 right-0 bg-white border-2 border-[#4B935E]/20 rounded-lg shadow-xl overflow-hidden z-50 min-w-[140px]">
          {/* Islamic geometric top border */}
          <div className="h-1 bg-gradient-to-r from-transparent via-[#4B935E] to-transparent"></div>
          
          {languages.map((language) => (
            <button
              key={language.code}
              onClick={() => handleLanguageChange(language.code)}
              className={`w-full flex items-center gap-3 px-4 py-3 text-left transition-all duration-300 hover:bg-[#4B935E]/10 ${
                locale === language.code 
                  ? 'bg-[#4B935E]/5 text-[#4B935E] font-semibold' 
                  : 'text-[#2C3E50] hover:text-[#4B935E]'
              }`}
            >
              <span className="text-lg">{language.flag}</span>
              <span className="text-sm font-medium">{language.name}</span>
              
              {/* Active indicator */}
              {locale === language.code && (
                <div className="ml-auto">
                  <svg className="w-4 h-4 text-[#4B935E]" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
              )}
            </button>
          ))}
          
          {/* Islamic geometric bottom border */}
          <div className="h-1 bg-gradient-to-r from-transparent via-[#4B935E] to-transparent"></div>
        </div>
      )}

      {/* Backdrop to close dropdown */}
      {isOpen && (
        <div 
          className="fixed inset-0 z-40" 
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
}
